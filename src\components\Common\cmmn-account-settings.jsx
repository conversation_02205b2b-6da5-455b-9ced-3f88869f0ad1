import {
  Button,
  Colors,
  EntityTitle,
  Menu,
  MenuItem,
  Tag,
} from "@blueprintjs/core";
import { collection, onSnapshot } from "firebase/firestore";
import { useContext, useEffect, useState } from "react";
import { auth, db } from "../../services/firebase";
import dayjs from "dayjs";

export default function CmmnAccountSettings({ user }) {
  const [selectedTab, setSelectedTab] = useState("informations");
  const [sessions, setSessions] = useState([]);

  useEffect(() => {
    onSnapshot(
      collection(db, "users", auth.currentUser.uid, "sessions"),
      (querySnapshot) => {
        setSessions(
          querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }))
        );
      }
    );
  }, []);

  if (!user) return "Loading...";
  return (
    <>
      <div style={{ display: "flex", minHeight: 300 }}>
        <div
          style={{
            backgroundColor: Colors.WHITE,
            padding: 5,
            borderRight: `1px solid ${Colors.LIGHT_GRAY1}`,
          }}
        >
          <Menu style={{ padding: 0 }}>
            <MenuItem
              text="Informations"
              icon="user"
              active={selectedTab === "informations"}
              onClick={() => setSelectedTab("informations")}
            />
            <MenuItem
              text="Authentication"
              icon="lock"
              active={selectedTab === "authentication"}
              onClick={() => setSelectedTab("authentication")}
            />
            <MenuItem
              text="Sessions"
              icon="list"
              active={selectedTab === "sessions"}
              onClick={() => setSelectedTab("sessions")}
            />
          </Menu>
        </div>
        <div style={{ flex: 1 }}>
          {selectedTab === "informations" && <>INFORMATIONS</>}
          {selectedTab === "authentication" && <>AUTHENTICATION</>}
          {selectedTab === "sessions" && (
            <>
              <div>
                <div style={{ padding: 15, fontWeight: 700 }}>Sessions</div>
                <div
                  style={{
                    overflow: "auto",
                    padding: "0 15px",
                    display: "flex",
                    flexFlow: "column",
                    gap: 15,
                  }}
                >
                  {sessions
                    .sort((a, b) => {
                      return (
                        b.createdAt.seconds * 1000 - a.createdAt.seconds * 1000
                      );
                    })
                    .map((sessions, index) => (
                      <div key={sessions.id}>
                        <EntityTitle
                          tags={
                            <Tag
                              minimal
                              intent={sessions.active ? "success" : "none"}
                            >
                              {sessions.active ? "Active" : "Inactive"}
                            </Tag>
                          }
                          fill
                          title={"Session"}
                          subtitle={dayjs(
                            sessions.createdAt.seconds * 1000
                          ).format("DD/MM/YYYY HH:mm:ss")}
                        />
                      </div>
                    ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}
