import {
  Button,
  ButtonGroup,
  <PERSON>lapse,
  Colors,
  Entity<PERSON><PERSON><PERSON>,
  Switch,
  Tab,
  Tabs,
} from "@blueprintjs/core";
import { useRef } from "react";
import Map, { NavigationControl, ScaleControl } from "react-map-gl/mapbox";
import { useOutletContext } from "react-router-dom";

export default function ClntMap() {
  const mapRef = useRef();

  const { sites } = useOutletContext();
  return (
    <>
      <div style={{ flex: 1 }}>
        <Map
          ref={mapRef}
          onLoad={(e) => {}}
          mapboxAccessToken={import.meta.env.VITE_MAPBOX_ACCESS_TOKEN}
          mapStyle={"mapbox://styles/mapbox/light-v11"}
          projection="mercator"
          logoPosition="bottom-right"
          attributionControl={false}
        >
          <ScaleControl />
          <div
            style={{
              position: "absolute",
              top: 10,
              right: 10,
              backgroundColor: Colors.WHITE,
              padding: 15,
              borderRadius: 2,
              border: `1px solid ${Colors.LIGHT_GRAY1}`,
              width: 300,
            }}
          ></div>
          <div style={{ bottom: 60, left: 10, position: "absolute" }}>
            <ButtonGroup vertical>
              <Button icon="plus" onClick={() => mapRef.current.zoomIn()} />
              <Button icon="minus" onClick={() => mapRef.current.zoomOut()} />
            </ButtonGroup>
          </div>
        </Map>
      </div>
    </>
  );
}
