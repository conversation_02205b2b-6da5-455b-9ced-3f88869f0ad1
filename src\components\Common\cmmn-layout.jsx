import {
  Button,
  Colors,
  Dialog,
  DialogStep,
  Divider,
  EntityTitle,
  FormGroup,
  InputGroup,
  Menu,
  MenuDivider,
  MenuItem,
  Popover,
  Tab,
  TabPanel,
  Tabs,
} from "@blueprintjs/core";
import { auth, db } from "../../services/firebase";
import { doc, onSnapshot, updateDoc } from "firebase/firestore";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import CmmnAccountSettings from "./cmmn-account-settings";
import { Outlet } from "react-router-dom";
import Logo from "./logo";

export default function CmmnLayout() {
  const [openAccountSettings, setOpenAccountSettings] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    onSnapshot(doc(db, "users", auth.currentUser.uid), (doc) => {
      setUser(doc.data());
    });
  }, []);

  if (!user) return "Loading...";

  return (
    <>
      <Dialog
        icon="user"
        title="Account Settings"
        isOpen={openAccountSettings}
        onClose={() => setOpenAccountSettings(false)}
        style={{ width: 600 }}
      >
        <CmmnAccountSettings user={user} />
      </Dialog>
      <div style={{ display: "flex", minHeight: "100svh", flexFlow: "column" }}>
        <div
          style={{
            padding: "10px 12px",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: `1px solid ${Colors.LIGHT_GRAY1}`,
          }}
        >
          <div>
            <Logo />
          </div>
          <div></div>
          <div>
            <Button icon="cog" variant="minimal" />
            <Button icon="notifications" variant="minimal" />
            <Popover
              placement="bottom-end"
              content={
                <Menu>
                  <MenuDivider
                    title={
                      <EntityTitle
                        title={auth.currentUser.displayName}
                        subtitle={auth.currentUser.email}
                      />
                    }
                  />
                  <MenuDivider />
                  <MenuItem
                    icon="user"
                    text="Account"
                    onClick={() => setOpenAccountSettings(true)}
                  />
                  <MenuItem icon="book" text="Documentation" />
                  <MenuItem icon="updated" text="Updates" />
                  <MenuDivider />
                  <MenuItem
                    icon="log-out"
                    text="Logout"
                    onClick={() => {
                      updateDoc(
                        doc(
                          db,
                          "users",
                          auth.currentUser.uid,
                          "sessions",
                          auth.currentUser.refreshToken
                        ),
                        {
                          active: false,
                          updatedAt: dayjs().toDate(),
                        }
                      ).then(() => {
                        auth.signOut();
                      });
                    }}
                  />
                </Menu>
              }
            >
              <Button icon="user" variant="minimal" />
            </Popover>
          </div>
        </div>
        <div style={{ flex: 1, overflow: "auto", display: "flex" }}>
          <Outlet context={{ user }} />
        </div>
      </div>
    </>
  );
}
