import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { initializeFirestore, persistentLocalCache } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyBg5JQSl82Dgx_WSrTSrH5i4EYVOCw0jg4",
  authDomain: "fmai-portal-v1-4ba1b.firebaseapp.com",
  projectId: "fmai-portal-v1-4ba1b",
  storageBucket: "fmai-portal-v1-4ba1b.firebasestorage.app",
  messagingSenderId: "140856363113",
  appId: "1:140856363113:web:2ebd59874cf9dba3953af8",
  measurementId: "G-9Q9L8LEQGL",
};

const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

export const auth = getAuth(app);
export const db = initializeFirestore(app, {
  localCache: persistentLocalCache({ cacheSizeBytes: 1024 * 1024 * 100 }),
});
