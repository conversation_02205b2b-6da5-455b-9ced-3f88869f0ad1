import { onAuthStateChanged } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { createContext, useEffect, useState } from "react";
import { auth, db } from "./services/firebase";

export const globalContext = createContext();

export default function GlobalContext(props) {
  const [loged, setLoged] = useState(null);

  onAuthStateChanged(auth, (user) => {
    if (user) {
      setLoged(true);
      getDoc(doc(db, "users", user.uid, "sessions", user.refreshToken)).then(
        (doc) => {
          if (doc.exists()) {
            if (!doc.data().active) {
              auth.signOut();
            }
          } else {
            auth.signOut();
          }
        }
      );
    } else {
      setLoged(false);
    }
  });

  if (loged === null) return "Loading...";

  return (
    <globalContext.Provider value={{ loged }}>
      {props.children}
    </globalContext.Provider>
  );
}
