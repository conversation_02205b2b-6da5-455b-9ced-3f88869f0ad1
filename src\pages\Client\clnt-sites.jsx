import { Button, ButtonGroup } from "@blueprintjs/core";
import ClntAddSite from "../../components/Client/clnt-add-site";

export default function ClntSites() {
  return (
    <>
      <div
        style={{
          padding: "10px 20px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div style={{ fontWeight: 700, fontSize: 20 }}>Sites</div>
        <div style={{ display: "flex", gap: 15 }}>
          <ButtonGroup>
            <Button text="Export" />
          </ButtonGroup>
          <ClntAddSite />
        </div>
      </div>
      <div style={{ flex: 1 }}>SITES LISTE</div>
    </>
  );
}
