import {
  <PERSON>ton,
  Colors,
  <PERSON>u,
  Menu<PERSON>ivider,
  MenuItem,
  Popover,
} from "@blueprintjs/core";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { auth, db } from "../../services/firebase";
import {
  collection,
  collectionGroup,
  onSnapshot,
  query,
  where,
} from "firebase/firestore";

export default function ClntLayout() {
  const [sites, setSites] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    onSnapshot(
      query(
        collectionGroup(db, "sites"),
        where("viewers", "array-contains", auth.currentUser.uid)
      ),
      (snapOnSites) => {
        setSites(
          snapOnSites.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }))
        );
      }
    );
  }, []);
  return (
    <>
      <div
        style={{
          borderRight: `1px solid ${Colors.LIGHT_GRAY1}`,
          display: "flex",
          flexFlow: "column",
        }}
      >
        <div style={{ flex: 1 }}>
          <Menu style={{}}>
            <MenuItem
              text="Dashboard"
              icon="dashboard"
              active={window.location.pathname === "/"}
              onClick={() => navigate("")}
            />
            <MenuItem
              text="Teams"
              icon="people"
              active={window.location.pathname === "/teams"}
              onClick={() => navigate("teams")}
            />
            <MenuItem
              text="Map view"
              icon="map"
              active={window.location.pathname === "/map"}
              onClick={() => navigate("map")}
            />
            <MenuDivider />
            <MenuItem
              text="Sites"
              icon="office"
              active={window.location.pathname === "/sites"}
              onClick={() => navigate("sites")}
            />
          </Menu>
        </div>
        <div></div>
      </div>
      <div
        style={{
          flex: 1,
          backgroundColor: Colors.LIGHT_GRAY5,
          display: "flex",
          flexFlow: "column",
        }}
      >
        <Outlet context={{ sites }} />
      </div>
    </>
  );
}
