import { useContext, useState } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import CmmnSignin from "./pages/Common/cmmn-signin";
import { globalContext } from "./global-context";
import CmmnLayout from "./components/Common/cmmn-layout";
import ClntLayout from "./components/Client/clnt-layout";
import ClntMap from "./pages/Client/clnt-map";
import ClntSites from "./pages/Client/clnt-sites";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route path="*" element={<Navigate to={"/"} />} />
      <Route>
        {loged ? (
          <Route>
            <Route element={<CmmnLayout />}>
              <Route element={<ClntLayout />}>
                <Route path="" element={"APP"} />
                <Route path="map" element={<ClntMap />} />
                <Route path="sites" element={<ClntSites />} />
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Public"} />
            <Route path="signin" element={<CmmnSignin />} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;
