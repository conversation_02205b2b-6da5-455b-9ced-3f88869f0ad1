import {
  <PERSON>ton,
  <PERSON>s,
  Dialog,
  DialogBody,
  DialogFooter,
  FormGroup,
  HTMLSelect,
  InputGroup,
} from "@blueprintjs/core";
import { AddressAutofill } from "@mapbox/search-js-react";
import { addDoc, collection } from "firebase/firestore";
import { useState } from "react";
import { db } from "../../services/firebase";
import dayjs from "dayjs";

export default function ClntAddSite() {
  const [open, setOpen] = useState(false);
  const [slectedAddress, setSelectedAddress] = useState(null);
  return (
    <>
      <Dialog
        icon="office"
        title="Create site"
        isOpen={open}
        onClose={() => {
          setOpen(false);
          setSelectedAddress(null);
        }}
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            addDoc(collection(db, "sites"), {
              name: event.target.name.value,
              code: event.target.code.value,
              type: event.target.type.value,
              address: event.target.address.value,
              city: event.target.city.value,
              zipCode: event.target.zipCode.value,
              country: event.target.country.value,
              latitude: event.target.latitude.value,
              longitude: event.target.longitude.value,
              createdAt: dayjs().toDate(),
              updatedAt: dayjs().toDate(),
              members: [auth.currentUser.uid],
              viewers: [auth.currentUser.uid],
              owners: [auth.currentUser.uid],
              active: true,
              createdBy: {
                uid: auth.currentUser.uid,
                displayName: auth.currentUser.displayName,
                email: auth.currentUser.email,
              },
            }).catch((error) => {
              console.log(error);
            });
          }}
        >
          <DialogBody>
            <FormGroup label="Site name">
              <InputGroup name="name" required />
            </FormGroup>
            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="Code" style={{ flex: 1 }}>
                <InputGroup name="code" required />
              </FormGroup>
              <FormGroup label="Type" style={{ flex: 1 }}>
                <HTMLSelect
                  name="type"
                  defaultValue={"agency"}
                  fill
                  style={{
                    backgroundColor: Colors.WHITE,
                    boxShadow:
                      "0 0 0 0 rgba(33, 93, 176, 0), 0 0 0 0 rgba(33, 93, 176, 0), inset 0 0 0 1px rgba(17, 20, 24, 0.2), inset 0 1px 1px rgba(17, 20, 24, 0.3)",
                  }}
                  options={[
                    {
                      value: "head-office",
                      label: "Head office",
                    },
                    {
                      value: "office",
                      label: "Office",
                    },
                    {
                      value: "agency",
                      label: "Agency",
                    },
                    {
                      value: "branch-office",
                      label: "Branch Office",
                    },
                    {
                      value: "factory",
                      label: "Factory",
                    },
                    {
                      value: "warehouse",
                      label: "Warehouse",
                    },
                    {
                      value: "hub",
                      label: "Hub",
                    },
                    {
                      value: "distribution-center",
                      label: "Distribution Center",
                    },
                    {
                      value: "training-center",
                      label: "Training Center",
                    },
                    {
                      value: "other",
                      label: "Other",
                    },
                  ]}
                />
              </FormGroup>
            </div>
            <AddressAutofill
              onRetrieve={(result) => {
                setSelectedAddress(result.features[0]);
              }}
              accessToken={import.meta.env.VITE_MAPBOX_ACCESS_TOKEN}
            >
              <FormGroup label="Address">
                <InputGroup
                  name="address"
                  leftIcon="search"
                  autoComplete="address-line1"
                />
              </FormGroup>
              <div style={{ display: "flex", gap: 15 }}>
                <FormGroup label="City" style={{ flex: 1 }}>
                  <InputGroup name="city" autoComplete="address-level2" />
                </FormGroup>
                <FormGroup label="Zip code" style={{ flex: 1 }}>
                  <InputGroup name="zipCode" autoComplete="postal-code" />
                </FormGroup>
              </div>
              <FormGroup label="Country" style={{ flex: 1 }}>
                <InputGroup name="country" autoComplete="country-name" />
              </FormGroup>
            </AddressAutofill>
            <div style={{ display: "flex", gap: 15 }}>
              <FormGroup label="Latitude" style={{ flex: 1 }}>
                <InputGroup
                  name="latitude"
                  defaultValue={slectedAddress?.geometry.coordinates[1]}
                />
              </FormGroup>
              <FormGroup label="Longitude" style={{ flex: 1 }}>
                <InputGroup
                  name="longitude"
                  defaultValue={slectedAddress?.geometry.coordinates[0]}
                />
              </FormGroup>
            </div>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button
                key={"cancel"}
                text="Cancel"
                onClick={() => setOpen(false)}
              />,
              <Button
                intent="primary"
                key={"create"}
                text="Create"
                type="submit"
              />,
            ]}
          />
        </form>
      </Dialog>
      <Button
        intent="primary"
        text="Create site"
        icon="add"
        onClick={() => setOpen(true)}
      />
    </>
  );
}
