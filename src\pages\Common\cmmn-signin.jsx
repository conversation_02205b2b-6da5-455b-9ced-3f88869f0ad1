import { Button, Colors, FormGroup, InputGroup } from "@blueprintjs/core";
import { auth, db } from "../../services/firebase";
import { signInWithEmailAndPassword } from "firebase/auth";
import { addDoc, collection, doc, setDoc } from "firebase/firestore";
import dayjs from "dayjs";
import { useState } from "react";

export default function CmmnSignin() {
  const [showPassword, setShowPassword] = useState(false);
  return (
    <>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100svh",
          backgroundColor: Colors.LIGHT_GRAY4,
        }}
      >
        <div style={{ width: 300 }}>
          <div style={{ marginBottom: 24 }}>
            <div style={{ fontSize: 20, fontWeight: 700 }}>Welcome back!</div>
            <div>Add your credentials to get started</div>
          </div>
          <form
            onSubmit={(event) => {
              event.preventDefault();
              signInWithEmailAndPassword(
                auth,
                event.target.email.value,
                event.target.password.value
              ).then((user) => {
                setDoc(
                  doc(
                    db,
                    "users",
                    user.user.uid,
                    "sessions",
                    user.user.refreshToken
                  ),
                  {
                    createdAt: dayjs().toDate(),
                    active: true,
                    device: navigator.userAgent,
                    updatedAt: dayjs().toDate(),
                  }
                );
              });
            }}
          >
            <FormGroup label="Email address">
              <InputGroup name="email" type="email" />
            </FormGroup>
            <FormGroup
              label="Password"
              helperText={
                <div style={{ display: "flex", justifyContent: "flex-end" }}>
                  <span style={{ cursor: "pointer" }}>
                    Forgot your password?
                  </span>
                </div>
              }
            >
              <InputGroup
                name="password"
                type={showPassword ? "text" : "password"}
                rightElement={
                  <Button
                    icon={showPassword ? "eye-open" : "eye-off"}
                    variant="minimal"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                }
              />
            </FormGroup>
            <Button
              style={{ marginTop: 24 }}
              text="Sign in"
              type="submit"
              intent="primary"
              fill
            />
          </form>
        </div>
      </div>
    </>
  );
}
